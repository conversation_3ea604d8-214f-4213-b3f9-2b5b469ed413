<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
    <!-- Floating hearts background -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute top-20 left-10 text-pink-200 text-2xl animate-float">💕</div>
      <div class="absolute top-40 right-20 text-purple-200 text-xl animate-float-delayed">💖</div>
      <div class="absolute bottom-32 left-20 text-pink-300 text-lg animate-float-slow">💗</div>
      <div class="absolute bottom-20 right-10 text-purple-300 text-2xl animate-float">💝</div>
      <div class="absolute top-60 left-1/2 text-pink-200 text-sm animate-float-delayed">💘</div>
    </div>

    <div class="max-w-md w-full space-y-8 relative z-10">
      <!-- Header with enhanced styling -->
      <div class="text-center animate-fade-in-up">
        <router-link to="/" class="inline-block group">
          <h1 class="text-5xl font-bold gradient-text mb-4 group-hover:scale-105 transition-transform duration-300">
            💘 Linda
          </h1>
        </router-link>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">
          Vytvořte si účet
        </h2>
        <p class="text-lg text-gray-600">
          Začněte svou cestu za láskou
        </p>
      </div>

      <!-- Enhanced form with glass effect -->
      <form @submit.prevent="handleRegister" class="mt-8 space-y-6 bg-white/80 backdrop-blur-lg rounded-2xl p-8 shadow-xl border border-white/20 animate-fade-in-up animation-delay-200">
        <div class="space-y-6">
          <div class="group">
            <label for="username" class="block text-sm font-semibold text-gray-700 mb-2 group-focus-within:text-pink-600 transition-colors">
              Uživatelské jméno
            </label>
            <div class="relative">
              <input
                id="username"
                v-model="form.username"
                type="text"
                required
                class="input-field-enhanced"
                placeholder="vase_jmeno"
                :disabled="loading"
              />
              <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
          </div>

          <div class="group">
            <label for="email" class="block text-sm font-semibold text-gray-700 mb-2 group-focus-within:text-pink-600 transition-colors">
              E-mailová adresa
            </label>
            <div class="relative">
              <input
                id="email"
                v-model="form.email"
                type="email"
                required
                class="input-field-enhanced"
                placeholder="<EMAIL>"
                :disabled="loading"
              />
              <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
            </div>
          </div>

          <div class="group">
            <label for="password" class="block text-sm font-semibold text-gray-700 mb-2 group-focus-within:text-pink-600 transition-colors">
              Heslo
            </label>
            <div class="relative">
              <input
                id="password"
                v-model="form.password"
                type="password"
                required
                class="input-field-enhanced"
                placeholder="••••••••"
                :disabled="loading"
              />
              <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
            </div>
            <div class="mt-2 text-sm text-gray-500 flex items-center">
              <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Minimálně 6 znaků
            </div>
          </div>

          <div class="group">
            <label for="confirmPassword" class="block text-sm font-semibold text-gray-700 mb-2 group-focus-within:text-pink-600 transition-colors">
              Potvrdit heslo
            </label>
            <div class="relative">
              <input
                id="confirmPassword"
                v-model="form.confirmPassword"
                type="password"
                required
                class="input-field-enhanced"
                placeholder="••••••••"
                :disabled="loading"
              />
              <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced checkbox -->
        <div class="flex items-start space-x-3 p-4 bg-gray-50/50 rounded-xl">
          <div class="flex items-center h-5">
            <input
              id="terms"
              v-model="form.acceptTerms"
              type="checkbox"
              required
              class="h-5 w-5 text-pink-600 focus:ring-pink-500 border-gray-300 rounded-md transition-all duration-200"
            />
          </div>
          <label for="terms" class="text-sm text-gray-700 leading-relaxed">
            Souhlasím s
            <a href="#" class="text-pink-600 hover:text-pink-700 font-medium underline decoration-pink-200 hover:decoration-pink-400 transition-colors">podmínkami použití</a>
            a
            <a href="#" class="text-pink-600 hover:text-pink-700 font-medium underline decoration-pink-200 hover:decoration-pink-400 transition-colors">zásadami ochrany soukromí</a>
          </label>
        </div>

        <!-- Enhanced submit button -->
        <div>
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="w-full btn-primary-enhanced flex justify-center items-center py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <div v-if="loading" class="loading-spinner-enhanced mr-3"></div>
            <svg v-if="!loading" class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
            {{ loading ? 'Registruji...' : 'Registrovat se' }}
          </button>
        </div>

        <!-- Enhanced login link -->
        <div class="text-center p-4 bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl">
          <span class="text-gray-600">Už máte účet? </span>
          <router-link to="/login" class="font-semibold text-pink-600 hover:text-pink-700 underline decoration-pink-200 hover:decoration-pink-400 transition-all duration-200">
            Přihlaste se zde
          </router-link>
        </div>
      </form>

      <!-- Enhanced Social Register -->
      <div class="mt-8 animate-fade-in-up animation-delay-400">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gradient-to-r from-pink-200 to-purple-200" />
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-4 bg-gradient-to-r from-pink-50 via-purple-50 to-indigo-50 text-gray-600 font-medium">Nebo se registrujte pomocí</span>
          </div>
        </div>

        <div class="mt-6 grid grid-cols-2 gap-4">
          <button
            type="button"
            class="group w-full inline-flex justify-center items-center py-3 px-4 border border-gray-200 rounded-xl shadow-sm bg-white/80 backdrop-blur-sm text-sm font-semibold text-gray-700 hover:bg-white hover:shadow-md hover:scale-105 transition-all duration-200"
          >
            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <span class="ml-2">Google</span>
          </button>

          <button
            type="button"
            class="group w-full inline-flex justify-center items-center py-3 px-4 border border-gray-200 rounded-xl shadow-sm bg-white/80 backdrop-blur-sm text-sm font-semibold text-gray-700 hover:bg-white hover:shadow-md hover:scale-105 transition-all duration-200"
          >
            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 24 24">
              <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 ********** 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
            </svg>
            <span class="ml-2">Apple</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'

export default {
  name: 'Register',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    const toast = useToast()

    const form = ref({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      acceptTerms: false
    })

    const loading = ref(false)

    const isFormValid = computed(() => {
      return form.value.username.length >= 3 &&
             form.value.email.includes('@') &&
             form.value.password.length >= 6 &&
             form.value.password === form.value.confirmPassword &&
             form.value.acceptTerms
    })

    const handleRegister = async () => {
      if (!isFormValid.value) {
        toast.error('Prosím vyplňte všechna pole správně')
        return
      }

      loading.value = true
      
      const result = await authStore.register(
        form.value.username,
        form.value.email,
        form.value.password
      )
      
      if (result.success) {
        toast.success('Účet byl úspěšně vytvořen!')
        router.push('/dashboard')
      } else {
        toast.error(result.error)
      }
      
      loading.value = false
    }

    return {
      form,
      loading,
      isFormValid,
      handleRegister
    }
  }
}
</script>
