.PHONY: help build up down logs clean test lint format

# Default target
help:
	@echo "Linda Dating App - Available commands:"
	@echo ""
	@echo "  build     - Build all Docker images"
	@echo "  up        - Start all services"
	@echo "  down      - Stop all services"
	@echo "  logs      - Show logs from all services"
	@echo "  clean     - Clean up Docker resources"
	@echo "  test      - Run all tests"
	@echo "  lint      - Run linting on all code"
	@echo "  format    - Format all code"
	@echo "  dev       - Start development environment"
	@echo "  prod      - Start production environment"
	@echo ""

# Docker commands
build:
	docker compose build

up:
	docker compose up -d

down:
	docker compose down

logs:
	docker compose logs -f

clean:
	docker compose down -v --remove-orphans
	docker system prune -f

# Development
dev:
	docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d

prod:
	docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Testing
test:
	@echo "Running backend tests..."
	cd backend && go test ./...
	@echo "Running frontend tests..."
	cd frontend && npm test
	@echo "Running AI service tests..."
	cd ai && python -m pytest

# Linting
lint:
	@echo "Linting Go code..."
	cd backend && golangci-lint run
	@echo "Linting Vue.js code..."
	cd frontend && npm run lint
	@echo "Linting Python code..."
	cd ai && flake8 .

# Formatting
format:
	@echo "Formatting Go code..."
	cd backend && gofmt -w .
	@echo "Formatting Vue.js code..."
	cd frontend && npm run format
	@echo "Formatting Python code..."
	cd ai && black .

# Database
db-migrate:
	docker compose exec api go run cmd/migrate/main.go

db-seed:
	docker compose exec api go run cmd/seed/main.go

db-reset:
	docker compose down db
	docker volume rm linda_postgres_data
	docker compose up -d db
	sleep 10
	make db-migrate
	make db-seed

# Monitoring
monitor:
	@echo "Opening monitoring dashboards..."
	@echo "Grafana: http://localhost:3001"
	@echo "Prometheus: http://localhost:9090"

# Health checks
health:
	@echo "Checking service health..."
	curl -f http://localhost:8080/health || echo "Backend: DOWN"
	curl -f http://localhost:5000/health || echo "AI Service: DOWN"
	curl -f http://localhost:3000 || echo "Frontend: DOWN"

# Backup
backup:
	docker compose exec db pg_dump -U postgres appdb > backup_$(shell date +%Y%m%d_%H%M%S).sql

# Restore
restore:
	@read -p "Enter backup file name: " file; \
	docker compose exec -T db psql -U postgres appdb < $$file

# SSL certificates (for production)
ssl:
	mkdir -p ssl
	openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
		-keyout ssl/private.key \
		-out ssl/certificate.crt \
		-subj "/C=CZ/ST=Prague/L=Prague/O=Linda Dating/CN=linda-dating.com"

# Install dependencies
install:
	@echo "Installing backend dependencies..."
	cd backend && go mod download
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "Installing AI service dependencies..."
	cd ai && pip install -r requirements.txt

# Quick start
start: build up
	@echo "Linda Dating App is starting..."
	@echo "Waiting for services to be ready..."
	sleep 30
	make health
	@echo ""
	@echo "🎉 Linda Dating App is ready!"
	@echo ""
	@echo "Frontend: http://localhost:3000"
	@echo "Backend API: http://localhost:8080"
	@echo "AI Service: http://localhost:5000"
	@echo "Grafana: http://localhost:3001 (admin/admin)"
	@echo "Prometheus: http://localhost:9090"

# Stop everything
stop: down
	@echo "Linda Dating App stopped."

# Full reset
reset: clean
	docker volume prune -f
	docker network prune -f
	@echo "Full reset completed."
