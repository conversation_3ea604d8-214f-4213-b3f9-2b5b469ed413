#!/usr/bin/env python3
"""
AI Microservice for Linda Dating App
Provides recommendation engine, content moderation, and AI features
"""

import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
# Note: MCP functionality removed - MCP server is only for Augment Code Agent

# FastAPI app
app = FastAPI(
    title="Linda AI Service",
    description="AI microservice for dating app recommendations and moderation",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class UserProfile(BaseModel):
    user_id: int
    age: int
    gender: str
    sexual_orientation: str
    bio: str
    interests: List[str] = []
    location: Optional[str] = None

class RecommendationRequest(BaseModel):
    user_id: int
    limit: int = 10
    exclude_user_ids: List[int] = []

class ModerationRequest(BaseModel):
    content: str
    content_type: str  # "text", "image", "profile"
    user_id: Optional[int] = None

class ModerationResult(BaseModel):
    is_appropriate: bool
    confidence: float
    reasons: List[str] = []
    suggested_action: str  # "approve", "review", "reject"

# Simple in-memory logging for AI service events
# Note: MCP functionality removed - MCP server is only for Augment Code Agent
ai_service_logs = []

def log_ai_event(event_type: str, content: str, metadata: dict = None):
    """Log AI service events locally"""
    ai_service_logs.append({
        "timestamp": datetime.utcnow().isoformat(),
        "event_type": event_type,
        "content": content,
        "metadata": metadata or {}
    })
    # Keep only last 1000 events
    if len(ai_service_logs) > 1000:
        ai_service_logs.pop(0)
    logger.info(f"AI Event [{event_type}]: {content}")

@app.on_event("startup")
async def startup_event():
    """Initialize AI service"""
    logger.info("Starting AI Service...")

    # Log startup event
    log_ai_event(
        "system_event",
        "AI Service started successfully",
        {"service": "ai", "event": "startup"}
    )

    logger.info("AI Service initialized successfully")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.post("/recommendations", response_model=List[Dict[str, Any]])
async def get_recommendations(request: RecommendationRequest):
    """Get user recommendations based on preferences and behavior"""
    try:
        # Log the recommendation request
        log_ai_event(
            "user_interaction",
            f"Recommendation request for user {request.user_id}",
            {
                "user_id": request.user_id,
                "limit": request.limit,
                "exclude_count": len(request.exclude_user_ids)
            }
        )
        
        # For now, return a simple mock recommendation
        # In a real implementation, this would use ML models
        recommendations = []
        for i in range(min(request.limit, 5)):
            recommendations.append({
                "user_id": 1000 + i,
                "compatibility_score": 0.85 - (i * 0.1),
                "reasons": ["Similar interests", "Compatible age range", "Geographic proximity"],
                "confidence": 0.9 - (i * 0.05)
            })
        
        return recommendations
        
    except Exception as e:
        logger.error(f"Error generating recommendations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/moderate", response_model=ModerationResult)
async def moderate_content(request: ModerationRequest):
    """Moderate content for appropriateness"""
    try:
        # Log the moderation request
        log_ai_event(
            "moderation",
            f"Content moderation request: {request.content_type}",
            {
                "content_type": request.content_type,
                "user_id": request.user_id,
                "content_length": len(request.content)
            }
        )
        
        # Simple rule-based moderation (in real app, use ML models)
        inappropriate_words = ["spam", "scam", "fake", "inappropriate"]
        is_appropriate = not any(word in request.content.lower() for word in inappropriate_words)
        
        confidence = 0.95 if is_appropriate else 0.85
        reasons = []
        if not is_appropriate:
            reasons = ["Contains potentially inappropriate content"]
        
        suggested_action = "approve" if is_appropriate else "review"
        
        result = ModerationResult(
            is_appropriate=is_appropriate,
            confidence=confidence,
            reasons=reasons,
            suggested_action=suggested_action
        )
        
        # Log the moderation result
        log_ai_event(
            "moderation",
            f"Moderation result: {suggested_action} (confidence: {confidence})",
            {
                "content_type": request.content_type,
                "user_id": request.user_id,
                "result": suggested_action,
                "confidence": confidence
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error moderating content: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/analyze-compatibility")
async def analyze_compatibility(user_a: UserProfile, user_b: UserProfile):
    """Analyze compatibility between two users"""
    try:
        # Log the compatibility analysis
        log_ai_event(
            "ai_analysis",
            f"Compatibility analysis between users {user_a.user_id} and {user_b.user_id}",
            {
                "user_a_id": user_a.user_id,
                "user_b_id": user_b.user_id,
                "analysis_type": "compatibility"
            }
        )
        
        # Simple compatibility scoring
        score = 0.0
        factors = []
        
        # Age compatibility
        age_diff = abs(user_a.age - user_b.age)
        if age_diff <= 5:
            score += 0.3
            factors.append("Compatible age range")
        elif age_diff <= 10:
            score += 0.15
            factors.append("Acceptable age difference")
        
        # Interest overlap
        common_interests = set(user_a.interests) & set(user_b.interests)
        if common_interests:
            score += 0.4 * (len(common_interests) / max(len(user_a.interests), len(user_b.interests)))
            factors.append(f"Shared interests: {', '.join(common_interests)}")
        
        # Bio similarity (simple keyword matching)
        bio_a_words = set(user_a.bio.lower().split())
        bio_b_words = set(user_b.bio.lower().split())
        bio_overlap = len(bio_a_words & bio_b_words) / len(bio_a_words | bio_b_words) if bio_a_words | bio_b_words else 0
        score += 0.3 * bio_overlap
        if bio_overlap > 0.2:
            factors.append("Similar interests mentioned in bio")
        
        # Normalize score
        score = min(score, 1.0)
        
        return {
            "compatibility_score": round(score, 2),
            "factors": factors,
            "recommendation": "high" if score > 0.7 else "medium" if score > 0.4 else "low"
        }
        
    except Exception as e:
        logger.error(f"Error analyzing compatibility: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats")
async def get_ai_stats():
    """Get AI service statistics"""
    try:
        # Get local AI service logs
        return {
            "total_events": len(ai_service_logs),
            "recent_events": ai_service_logs[-10:] if ai_service_logs else [],
            "service_status": "healthy",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5000)
